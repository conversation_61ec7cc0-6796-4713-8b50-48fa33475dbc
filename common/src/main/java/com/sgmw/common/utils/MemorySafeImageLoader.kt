package com.sgmw.common.utils

import android.content.Context
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.DownsampleStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import java.lang.ref.WeakReference

/**
 * 内存安全的图片加载器
 * 专门解决图片加载导致的内存泄漏问题
 * 
 * 主要解决的问题：
 * 1. Context引用泄漏
 * 2. 回调函数持有外部引用
 * 3. 协程持有Activity引用
 * 4. ImageView引用未及时释放
 */
object MemorySafeImageLoader {
    
    private const val TAG = "MemorySafeImageLoader"
    
    /**
     * 内存安全的图片加载
     * 使用弱引用避免内存泄漏
     */
    fun loadImageSafely(
        context: Context,
        url: String,
        imageView: ImageView,
        placeholder: Int = 0,
        errorResId: Int = 0,
        onSuccess: (() -> Unit)? = null,
        onError: (() -> Unit)? = null
    ) {
        // 使用弱引用避免持有ImageView引用导致内存泄漏
        val imageViewRef = WeakReference(imageView)
        val contextRef = WeakReference(context)
        
        // 检查引用是否有效
        val validImageView = imageViewRef.get()
        val validContext = contextRef.get()
        
        if (validImageView == null || validContext == null) {
            Log.w(TAG, "ImageView或Context已被回收，跳过图片加载")
            onError?.invoke()
            return
        }
        
        try {
            // 创建内存友好的RequestOptions
            val options = createMemorySafeOptions(placeholder, errorResId)
            
            // 使用内存安全的监听器
            val safeListener = createMemorySafeListener(imageViewRef, onSuccess, onError)
            
            Glide.with(validContext)
                .load(url)
                .apply(options)
                .listener(safeListener)
                .into(validImageView)
                
        } catch (e: OutOfMemoryError) {
            Log.e(TAG, "OOM during image loading: $url", e)
            handleOOMSafely(contextRef, imageViewRef, url, errorResId, onError)
        } catch (e: Exception) {
            Log.e(TAG, "Error during image loading: $url", e)
            onError?.invoke()
        }
    }
    
    /**
     * 内存安全的圆角图片加载
     */
    fun loadRoundImageSafely(
        context: Context,
        url: String,
        imageView: ImageView,
        radius: Int,
        placeholder: Int = 0,
        errorResId: Int = 0,
        onSuccess: (() -> Unit)? = null,
        onError: (() -> Unit)? = null
    ) {
        val imageViewRef = WeakReference(imageView)
        val contextRef = WeakReference(context)
        
        val validImageView = imageViewRef.get()
        val validContext = contextRef.get()
        
        if (validImageView == null || validContext == null) {
            Log.w(TAG, "ImageView或Context已被回收，跳过圆角图片加载")
            onError?.invoke()
            return
        }
        
        try {
            val options = createMemorySafeOptions(placeholder, errorResId)
                .transform(RoundedCorners(radius))
                .centerCrop()
            
            val safeListener = createMemorySafeListener(imageViewRef, onSuccess, onError)
            
            Glide.with(validContext)
                .load(url)
                .apply(options)
                .listener(safeListener)
                .into(validImageView)
                
        } catch (e: OutOfMemoryError) {
            Log.e(TAG, "OOM during round image loading: $url", e)
            handleOOMSafely(contextRef, imageViewRef, url, errorResId, onError)
        } catch (e: Exception) {
            Log.e(TAG, "Error during round image loading: $url", e)
            onError?.invoke()
        }
    }
    
    /**
     * 创建内存安全的RequestOptions
     */
    private fun createMemorySafeOptions(placeholder: Int, errorResId: Int): RequestOptions {
        val options = RequestOptions()
            .format(DecodeFormat.PREFER_RGB_565) // 使用RGB_565减少内存占用
            .downsample(DownsampleStrategy.AT_MOST)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .skipMemoryCache(false) // 使用内存缓存，但控制大小
        
        // 根据内存状态调整图片尺寸
        val maxSize = if (isMemoryPressureHigh()) 300 else 600
        options.override(maxSize, maxSize)
        
        if (placeholder != 0) options.placeholder(placeholder)
        if (errorResId != 0) options.error(errorResId)
        
        return options
    }
    
    /**
     * 创建内存安全的监听器
     * 使用弱引用避免持有外部对象引用
     */
    private fun createMemorySafeListener(
        imageViewRef: WeakReference<ImageView>,
        onSuccess: (() -> Unit)?,
        onError: (() -> Unit)?
    ): RequestListener<android.graphics.drawable.Drawable> {
        
        return object : RequestListener<android.graphics.drawable.Drawable> {
            override fun onLoadFailed(
                e: GlideException?,
                model: Any?,
                target: Target<android.graphics.drawable.Drawable>,
                isFirstResource: Boolean
            ): Boolean {
                // 检查引用是否仍然有效
                if (imageViewRef.get() != null) {
                    onError?.invoke()
                }
                Log.w(TAG, "图片加载失败: $model", e)
                return false
            }
            
            override fun onResourceReady(
                resource: android.graphics.drawable.Drawable,
                model: Any,
                target: Target<android.graphics.drawable.Drawable>?,
                dataSource: DataSource,
                isFirstResource: Boolean
            ): Boolean {
                // 检查引用是否仍然有效
                if (imageViewRef.get() != null) {
                    onSuccess?.invoke()
                }
                Log.d(TAG, "图片加载成功: $model")
                return false
            }
        }
    }
    
    /**
     * 内存安全的OOM处理
     */
    private fun handleOOMSafely(
        contextRef: WeakReference<Context>,
        imageViewRef: WeakReference<ImageView>,
        url: String,
        errorResId: Int,
        onError: (() -> Unit)?
    ) {
        val validContext = contextRef.get()
        val validImageView = imageViewRef.get()
        
        if (validContext == null || validImageView == null) {
            Log.w(TAG, "Context或ImageView已被回收，跳过OOM恢复")
            onError?.invoke()
            return
        }
        
        try {
            // 使用极小的配置重新尝试加载
            val emergencyOptions = RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565)
                .override(100, 100) // 极小尺寸
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
            
            Glide.with(validContext)
                .load(url)
                .apply(emergencyOptions)
                .into(validImageView)
                
        } catch (e: Exception) {
            Log.e(TAG, "OOM恢复失败: $url", e)
            if (errorResId != 0) {
                validImageView.setImageResource(errorResId)
            }
            onError?.invoke()
        }
    }
    
    /**
     * 清理指定ImageView的加载任务
     * 在Fragment/Activity销毁时调用，避免内存泄漏
     */
    fun clearImageView(context: Context, imageView: ImageView) {
        try {
            Glide.with(context).clear(imageView)
            Log.d(TAG, "已清理ImageView的加载任务")
        } catch (e: Exception) {
            Log.e(TAG, "清理ImageView失败", e)
        }
    }
    
    /**
     * 批量清理多个ImageView
     */
    fun clearImageViews(context: Context, vararg imageViews: ImageView) {
        imageViews.forEach { imageView ->
            clearImageView(context, imageView)
        }
    }
    
    /**
     * 在Fragment/Activity的onDestroy中调用
     * 清理所有相关的图片加载任务
     */
    fun onDestroy(context: Context) {
        try {
            // 清理该Context相关的所有Glide请求
            Glide.with(context).onDestroy()
            Log.d(TAG, "已清理Context相关的所有图片加载任务")
        } catch (e: Exception) {
            Log.e(TAG, "清理Context失败", e)
        }
    }
}

/**
 * ImageView扩展函数 - 内存安全版本
 */
fun ImageView.loadImageSafely(
    url: String,
    placeholder: Int = 0,
    errorResId: Int = 0,
    onSuccess: (() -> Unit)? = null,
    onError: (() -> Unit)? = null
) {
    MemorySafeImageLoader.loadImageSafely(
        context = this.context,
        url = url,
        imageView = this,
        placeholder = placeholder,
        errorResId = errorResId,
        onSuccess = onSuccess,
        onError = onError
    )
}

/**
 * ImageView扩展函数 - 内存安全的圆角图片加载
 */
fun ImageView.loadRoundImageSafely(
    url: String,
    radius: Int,
    placeholder: Int = 0,
    errorResId: Int = 0,
    onSuccess: (() -> Unit)? = null,
    onError: (() -> Unit)? = null
) {
    MemorySafeImageLoader.loadRoundImageSafely(
        context = this.context,
        url = url,
        imageView = this,
        radius = radius,
        placeholder = placeholder,
        errorResId = errorResId,
        onSuccess = onSuccess,
        onError = onError
    )
}
